package com.example.xposedimgui;

import android.app.Activity;
import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.text.InputType;
import android.util.Log;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;

/**
 * 输入法和剪贴板辅助类
 * 参考: https://github.com/By-Kon/AndroidImGui-Input
 */
public class InputHelper {
    private static final String TAG = "InputHelper";
    
    private static Activity currentActivity;
    private static EditText hiddenEditText;
    private static InputMethodManager inputMethodManager;
    private static ClipboardManager clipboardManager;
    
    /**
     * 初始化输入法辅助
     * @param activity 当前Activity
     */
    public static void init(Activity activity) {
        currentActivity = activity;

        // 获取输入法管理器
        inputMethodManager = (InputMethodManager) activity.getSystemService(Context.INPUT_METHOD_SERVICE);

        // 获取剪贴板管理器
        clipboardManager = (ClipboardManager) activity.getSystemService(Context.CLIPBOARD_SERVICE);

        // 创建隐藏的EditText用于输入法调用
        createHiddenEditText();

        // 设置JVM指针给C++
        try {
            System.loadLibrary("core");
            setJavaVM(getJVMPointer());
        } catch (Exception e) {
            Log.e(TAG, "Failed to set JVM pointer", e);
        }

        Log.d(TAG, "InputHelper initialized");
    }

    /**
     * 获取JVM指针（通过反射）
     */
    private static long getJVMPointer() {
        try {
            // 通过反射获取当前线程的JVM指针
            Class<?> vmClass = Class.forName("dalvik.system.VMRuntime");
            Object vmRuntime = vmClass.getMethod("getRuntime").invoke(null);

            // 尝试通过JNI获取JavaVM指针
            // 这里使用一个更可靠的方法
            return getCurrentJavaVM();
        } catch (Exception e) {
            Log.e(TAG, "Failed to get JVM pointer", e);
            return 0;
        }
    }

    /**
     * 通过JNI获取当前JavaVM指针
     */
    private static native long getCurrentJavaVM();
    
    /**
     * 创建隐藏的EditText
     */
    private static void createHiddenEditText() {
        if (currentActivity != null) {
            currentActivity.runOnUiThread(() -> {
                hiddenEditText = new EditText(currentActivity);
                hiddenEditText.setVisibility(View.GONE);
                hiddenEditText.setInputType(InputType.TYPE_CLASS_TEXT);
                
                // 添加到Activity的根视图
                if (currentActivity.findViewById(android.R.id.content) != null) {
                    ((android.view.ViewGroup) currentActivity.findViewById(android.R.id.content))
                        .addView(hiddenEditText);
                }
                
                Log.d(TAG, "Hidden EditText created");
            });
        }
    }
    
    /**
     * 显示输入法
     * @param currentText 当前文本
     * @param callback 输入完成回调
     */
    public static void showInputMethod(String currentText, InputCallback callback) {
        if (currentActivity == null || hiddenEditText == null || inputMethodManager == null) {
            Log.e(TAG, "InputHelper not initialized");
            return;
        }

        currentActivity.runOnUiThread(() -> {
            try {
                // 设置当前文本
                hiddenEditText.setText(currentText);
                hiddenEditText.setSelection(currentText.length());

                // 请求焦点
                hiddenEditText.requestFocus();

                // 显示输入法
                inputMethodManager.showSoftInput(hiddenEditText, InputMethodManager.SHOW_FORCED);

                // 设置输入完成监听
                hiddenEditText.setOnEditorActionListener((v, actionId, event) -> {
                    String inputText = hiddenEditText.getText().toString().toUpperCase();
                    hideInputMethod();

                    // 调用JNI回调
                    onInputComplete(inputText);

                    if (callback != null) {
                        callback.onInputComplete(inputText);
                    }

                    return true;
                });

                Log.d(TAG, "Input method shown with text: " + currentText);
            } catch (Exception e) {
                Log.e(TAG, "Failed to show input method", e);
            }
        });
    }
    
    /**
     * 隐藏输入法
     */
    public static void hideInputMethod() {
        if (currentActivity == null || hiddenEditText == null || inputMethodManager == null) {
            return;
        }
        
        currentActivity.runOnUiThread(() -> {
            try {
                inputMethodManager.hideSoftInputFromWindow(hiddenEditText.getWindowToken(), 0);
                hiddenEditText.clearFocus();
                Log.d(TAG, "Input method hidden");
            } catch (Exception e) {
                Log.e(TAG, "Failed to hide input method", e);
            }
        });
    }
    
    /**
     * 复制文本到剪贴板
     * @param text 要复制的文本
     * @param label 标签
     * @return 是否成功
     */
    public static boolean copyToClipboard(String text, String label) {
        if (clipboardManager == null) {
            Log.e(TAG, "ClipboardManager not initialized");
            return false;
        }
        
        try {
            ClipData clip = ClipData.newPlainText(label != null ? label : "复制内容", text);
            clipboardManager.setPrimaryClip(clip);
            Log.d(TAG, "Text copied to clipboard: " + text);
            return true;
        } catch (Exception e) {
            Log.e(TAG, "Failed to copy to clipboard", e);
            return false;
        }
    }
    
    /**
     * 从剪贴板获取文本
     * @return 剪贴板中的文本
     */
    public static String getFromClipboard() {
        if (clipboardManager == null) {
            Log.e(TAG, "ClipboardManager not initialized");
            return "";
        }
        
        try {
            ClipData clip = clipboardManager.getPrimaryClip();
            if (clip != null && clip.getItemCount() > 0) {
                CharSequence text = clip.getItemAt(0).getText();
                return text != null ? text.toString() : "";
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to get from clipboard", e);
        }
        
        return "";
    }
    
    /**
     * 输入完成回调接口
     */
    public interface InputCallback {
        void onInputComplete(String text);
    }
    
    /**
     * JNI调用接口 - 复制到剪贴板
     */
    public static boolean jniCopyToClipboard(String text) {
        return copyToClipboard(text, "设备ID");
    }
    
    /**
     * JNI调用接口 - 显示输入法
     */
    public static void jniShowInputMethod(String currentText) {
        showInputMethod(currentText, null);
    }
    
    /**
     * JNI调用接口 - 隐藏输入法
     */
    public static void jniHideInputMethod() {
        hideInputMethod();
    }

    /**
     * JNI调用接口 - 从剪贴板获取文本
     */
    public static String jniGetFromClipboard() {
        return getFromClipboard();
    }

    /**
     * JNI调用接口 - 粘贴并设置激活码
     */
    public static void jniPasteActivationCode() {
        String clipboardText = getFromClipboard();
        if (clipboardText != null && !clipboardText.isEmpty()) {
            // 格式化激活码
            String formatted = formatActivationCode(clipboardText);
            if (!formatted.isEmpty()) {
                // 调用JNI回调更新激活码
                onInputComplete(formatted);
                Log.d(TAG, "Pasted and formatted activation code: " + formatted);
            } else {
                Log.w(TAG, "Invalid activation code format in clipboard: " + clipboardText);
            }
        } else {
            Log.w(TAG, "Clipboard is empty or null");
        }
    }

    /**
     * 格式化激活码
     */
    private static String formatActivationCode(String input) {
        if (input == null) return "";

        // 移除所有非字母数字字符，转换为大写
        String cleaned = input.replaceAll("[^A-Za-z0-9]", "").toUpperCase();

        // 检查长度
        if (cleaned.length() < 16) {
            return ""; // 长度不足
        }

        // 取前16个字符并格式化为XXXX-XXXX-XXXX-XXXX
        StringBuilder formatted = new StringBuilder();
        for (int i = 0; i < 16; i++) {
            if (i > 0 && i % 4 == 0) {
                formatted.append("-");
            }
            formatted.append(cleaned.charAt(i));
        }

        return formatted.toString();
    }

    // JNI方法声明
    public static native void setJavaVM(long jvm);
    public static native void onInputComplete(String inputText);
    public static native String getCurrentActivationCode();
    private static native long getCurrentJavaVM();
}
