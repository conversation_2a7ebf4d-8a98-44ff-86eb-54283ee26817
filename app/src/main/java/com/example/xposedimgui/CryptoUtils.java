package com.example.xposedimgui;

import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.util.Base64;
import android.util.Log;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;

/**
 * AES加密工具类 - 与后端Node.js crypto完全兼容
 */
public class CryptoUtils {
    private static final String TAG = "CryptoUtils";
    
    // 与后端保持一致的密钥和IV
    private static final String FIXED_ENCRYPTION_KEY = "XwzcActivation24"; // 16字节
    private static final String FIXED_ENCRYPTION_IV = "InitVector123456";  // 16字节
    
    private static final String ALGORITHM = "AES";
    private static final String TRANSFORMATION = "AES/CBC/PKCS5Padding";

    /**
     * AES-128-CBC加密
     * @param plaintext 明文
     * @return Base64编码的密文，失败返回null
     */
    public static String aesEncrypt(String plaintext) {
        try {
            // 创建密钥和IV
            SecretKeySpec keySpec = new SecretKeySpec(
                FIXED_ENCRYPTION_KEY.getBytes(StandardCharsets.UTF_8), 
                ALGORITHM
            );
            IvParameterSpec ivSpec = new IvParameterSpec(
                FIXED_ENCRYPTION_IV.getBytes(StandardCharsets.UTF_8)
            );
            
            // 创建加密器
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);
            
            // 加密
            byte[] encrypted = cipher.doFinal(plaintext.getBytes(StandardCharsets.UTF_8));
            
            // Base64编码
            return Base64.encodeToString(encrypted, Base64.NO_WRAP);
            
        } catch (Exception e) {
            Log.e(TAG, "AES encryption failed", e);
            return null;
        }
    }

    /**
     * AES-128-CBC解密
     * @param ciphertext Base64编码的密文
     * @return 明文，失败返回null
     */
    public static String aesDecrypt(String ciphertext) {
        try {
            // Base64解码
            byte[] encryptedData = Base64.decode(ciphertext, Base64.NO_WRAP);
            
            // 创建密钥和IV
            SecretKeySpec keySpec = new SecretKeySpec(
                FIXED_ENCRYPTION_KEY.getBytes(StandardCharsets.UTF_8), 
                ALGORITHM
            );
            IvParameterSpec ivSpec = new IvParameterSpec(
                FIXED_ENCRYPTION_IV.getBytes(StandardCharsets.UTF_8)
            );
            
            // 创建解密器
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);
            
            // 解密
            byte[] decrypted = cipher.doFinal(encryptedData);
            
            return new String(decrypted, StandardCharsets.UTF_8);
            
        } catch (Exception e) {
            Log.e(TAG, "AES decryption failed", e);
            return null;
        }
    }

    /**
     * 复制文本到剪贴板
     * @param context 上下文
     * @param text 要复制的文本
     * @return 是否复制成功
     */
    public static boolean copyToClipboard(Context context, String text) {
        try {
            ClipboardManager clipboard = (ClipboardManager) context.getSystemService(Context.CLIPBOARD_SERVICE);
            if (clipboard != null) {
                ClipData clip = ClipData.newPlainText("设备ID", text);
                clipboard.setPrimaryClip(clip);
                Log.d(TAG, "Text copied to clipboard: " + text);
                return true;
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to copy to clipboard", e);
        }
        return false;
    }

    /**
     * 测试加密解密功能
     */
    public static boolean testCrypto() {
        String testData = "{\"action\":\"activate\",\"deviceId\":\"AND-12345678\",\"activationCode\":\"TEST-1234-5678-9ABC\",\"platform\":\"android\"}";

        Log.d(TAG, "原始数据: " + testData);

        String encrypted = aesEncrypt(testData);
        Log.d(TAG, "加密数据: " + encrypted);

        if (encrypted != null) {
            String decrypted = aesDecrypt(encrypted);
            Log.d(TAG, "解密数据: " + decrypted);
            boolean success = testData.equals(decrypted);
            Log.d(TAG, "加密解密是否成功: " + success);
            return success;
        }

        Log.e(TAG, "加密失败");
        return false;
    }
}
