package com.hook.xposedimgui

import android.annotation.SuppressLint
import android.app.Activity
import android.app.Application
import android.content.Context
import android.os.Bundle
import android.util.Log
import de.robv.android.xposed.IXposedHookLoadPackage
import de.robv.android.xposed.IXposedHookZygoteInit
import de.robv.android.xposed.callbacks.XC_LoadPackage
import java.io.File
import android.provider.Settings
import de.robv.android.xposed.XposedHelpers
import android.app.AlertDialog
import android.text.InputType
import android.widget.EditText

class Core : IXposedHookLoadPackage, IXposedHookZygoteInit {

    var modulePath = ""

    override fun initZygote(p0: IXposedHookZygoteInit.StartupParam?) {
        modulePath = p0?.modulePath ?: ""
    }

    @SuppressLint("UnsafeDynamicallyLoadedCode")
    override fun handleLoadPackage(p0: XC_LoadPackage.LoadPackageParam) {
        // Hook Application.attach 来加载native库
        Application::class.java.hook("attach", Context::class.java, after = { args ->
            args.obj<Application>().packageManager.let {
                try {
                    val moduleFile = File(modulePath).parent
                    System.load(File(moduleFile, "lib/arm64/libcore.so").absolutePath)
                    Log.d("XposedImgui", "Native library loaded successfully")
                } catch (e: Throwable) {
                    Log.e("XposedImgui", "Failed to load native library", e)
                }
            }
        })
    }

    companion object {
        @JvmStatic
        fun getDeviceID(context: Context): String {
            return Settings.Secure.getString(context.contentResolver, Settings.Secure.ANDROID_ID)
        }

        // JNI Native a方法
        external fun setKeyboardResult(text: String)

        @JvmStatic
        fun showKeyboard(context: Context, initialText: String) {
            // 确保在主线程运行
            XposedHelpers.callStaticMethod(XposedHelpers.findClass("android.app.ActivityThread", null), "currentActivityThread")
            val mainHandler = XposedHelpers.getObjectField(XposedHelpers.callStaticMethod(XposedHelpers.findClass("android.app.ActivityThread", null), "currentActivityThread"), "mH") as android.os.Handler
            mainHandler.post {
                val input = EditText(context)
                input.inputType = InputType.TYPE_CLASS_TEXT
                input.setText(initialText)

                AlertDialog.Builder(context)
                    .setTitle("输入激活码")
                    .setView(input)
                    .setPositiveButton("确定") { _, _ ->
                        setKeyboardResult(input.text.toString())
                    }
                    .setNegativeButton("取消", null)
                    .show()
            }
        }
    }
}