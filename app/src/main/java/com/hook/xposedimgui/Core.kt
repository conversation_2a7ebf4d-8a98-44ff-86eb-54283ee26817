package com.hook.xposedimgui

import android.annotation.SuppressLint
import android.app.Activity
import android.app.Application
import android.content.Context
import android.os.Bundle
import android.util.Log
import de.robv.android.xposed.IXposedHookLoadPackage
import de.robv.android.xposed.IXposedHookZygoteInit
import de.robv.android.xposed.callbacks.XC_LoadPackage
import java.io.File

class Core : IXposedHookLoadPackage, IXposedHookZygoteInit {

    var modulePath = ""

    override fun initZygote(p0: IXposedHookZygoteInit.StartupParam?) {
        modulePath = p0?.modulePath ?: ""
    }

    @SuppressLint("UnsafeDynamicallyLoadedCode")
    override fun handleLoadPackage(p0: XC_LoadPackage.LoadPackageParam) {
        // Hook Application.attach 来加载native库
        Application::class.java.hook("attach", Context::class.java, after = { args ->
            args.obj<Application>().packageManager.let {
                try {
                    val moduleFile = File(modulePath).parent
                    System.load(File(moduleFile, "lib/arm64/libcore.so").absolutePath)
                    Log.d("XposedImgui", "Native library loaded successfully")
                } catch (e: Throwable) {
                    Log.e("XposedImgui", "Failed to load native library", e)
                }
            }
        })
    }
}