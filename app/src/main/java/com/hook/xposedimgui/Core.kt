package com.hook.xposedimgui

import android.annotation.SuppressLint
import android.app.Activity
import android.app.Application
import android.content.Context
import android.os.Bundle
import android.util.Log
import com.example.xposedimgui.InputHelper
import de.robv.android.xposed.IXposedHookLoadPackage
import de.robv.android.xposed.IXposedHookZygoteInit
import de.robv.android.xposed.callbacks.XC_LoadPackage
import java.io.File

class Core : IXposedHookLoadPackage, IXposedHookZygoteInit {

    var modulePath = ""
    private var isInputHelperInitialized = false

    override fun initZygote(p0: IXposedHookZygoteInit.StartupParam?) {
        modulePath = p0?.modulePath ?: ""
    }

    @SuppressLint("UnsafeDynamicallyLoadedCode")
    override fun handleLoadPackage(p0: XC_LoadPackage.LoadPackageParam) {
        // Hook Application.attach 来加载native库
        Application::class.java.hook("attach", Context::class.java, after = { args ->
            args.obj<Application>().packageManager.let {
                try {
                    val moduleFile = File(modulePath).parent
                    System.load(File(moduleFile, "lib/arm64/libcore.so").absolutePath)
                    Log.d("XposedImgui", "Native library loaded successfully")
                } catch (e: Throwable) {
                    Log.e("XposedImgui", "Failed to load native library", e)
                }
            }
        })

        // Hook Activity.onCreate 来初始化InputHelper
        Activity::class.java.hook("onCreate", Bundle::class.java, after = { args ->
            val activity = args.obj<Activity>()

            // 只在第一个Activity中初始化一次
            if (!isInputHelperInitialized) {
                try {
                    InputHelper.init(activity)
                    isInputHelperInitialized = true
                    Log.d("XposedImgui", "InputHelper initialized successfully in ${activity.javaClass.simpleName}")
                } catch (e: Throwable) {
                    Log.e("XposedImgui", "Failed to initialize InputHelper", e)
                }
            }
        })

        // Hook Activity.onResume 来确保InputHelper在Activity恢复时可用
        Activity::class.java.hook("onResume", after = { args ->
            val activity = args.obj<Activity>()

            // 如果InputHelper还没有初始化，尝试初始化
            if (!isInputHelperInitialized) {
                try {
                    InputHelper.init(activity)
                    isInputHelperInitialized = true
                    Log.d("XposedImgui", "InputHelper initialized on resume in ${activity.javaClass.simpleName}")
                } catch (e: Throwable) {
                    Log.e("XposedImgui", "Failed to initialize InputHelper on resume", e)
                }
            }
        })
    }
}