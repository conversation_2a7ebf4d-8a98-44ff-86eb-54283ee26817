#pragma once

#include <string>
#include "imgui.h"

// 激活系统模块
namespace Activation {
    // 激活状态枚举
    enum class Status {
        NotInitialized,   // 尚未开始验证流程
        Verifying,        // 验证中
        Success,          // 激活成功
        Failed,           // 激活失败
        NetworkError,     // 网络错误
        Expired           // 已过期
    };

    // 全局状态变量 (extern声明)
    extern Status g_status;
    extern bool g_is_activated; // 用于外部快速判断
    extern std::string g_user_name;
    extern int g_expire_days;

    //================ 函数声明 ================//
    void Init(); // 轻量级初始化
    void DrawUI(); // 负责驱动验证和绘制UI
} 