#pragma once

#include "imgui.h"
#include <string>
#include <cstdint>
#include <chrono>

// JNI前向声明
struct _JavaVM;
typedef struct _JavaVM JavaVM;

namespace ActivationManager {

    // 激活状态枚举
    enum class ActivationStatus {
        NotActivated,    // 未激活
        Activated,       // 已激活
        Expired,         // 已过期
        Invalid          // 无效激活码
    };

    // 激活信息结构
    struct ActivationInfo {
        std::string deviceId;           // 设备ID
        std::string activationCode;     // 激活码
        std::string userName;           // 用户名
        std::chrono::system_clock::time_point activatedTime;  // 激活时间
        std::chrono::system_clock::time_point expireTime;     // 过期时间
        ActivationStatus status;        // 激活状态
        
        ActivationInfo() : status(ActivationStatus::NotActivated) {}
    };

    //================ 全局变量 ================//
    extern ActivationInfo g_activationInfo;
    extern bool g_showActivationWindow;
    extern char g_inputActivationCode[256];
    extern std::string g_statusMessage;
    extern ImVec4 g_statusColor;
    extern bool g_networkVerificationInProgress;

    //================ 函数声明 ================//
    
    /**
     * @brief 初始化激活管理器
     */
    void Init();

    /**
     * @brief 生成设备ID
     * @return 设备唯一标识符
     */
    std::string GenerateDeviceId();

    /**
     * @brief 验证激活码
     * @param activationCode 激活码
     * @return 验证结果
     */
    bool ValidateActivationCode(const std::string& activationCode);

    /**
     * @brief 激活设备
     * @param activationCode 激活码
     * @return 激活是否成功
     */
    bool ActivateDevice(const std::string& activationCode);

    /**
     * @brief 检查激活状态
     * @return 当前激活状态
     */
    ActivationStatus CheckActivationStatus();

    /**
     * @brief 保存激活信息到文件
     */
    void SaveActivationInfo();

    /**
     * @brief 从文件加载激活信息
     */
    void LoadActivationInfo();

    /**
     * @brief 绘制激活界面
     */
    void DrawActivationUI();

    /**
     * @brief 检查是否已激活
     * @return true表示已激活且未过期
     */
    bool IsActivated();

    /**
     * @brief 获取剩余天数
     * @return 剩余天数，-1表示已过期
     */
    int GetRemainingDays();

    /**
     * @brief 格式化时间为字符串
     * @param timePoint 时间点
     * @return 格式化的时间字符串
     */
    std::string FormatTime(const std::chrono::system_clock::time_point& timePoint);

    /**
     * @brief 复制文本到剪贴板
     * @param text 要复制的文本
     */
    void CopyToClipboard(const std::string& text);

    /**
     * @brief 网络验证激活状态
     * @param silent 是否静默验证（不显示状态消息）
     */
    void VerifyActivationOnline(bool silent = false);

    /**
     * @brief 设置JavaVM指针（JNI调用）
     */
    void setJavaVM(JavaVM* jvm);

    /**
     * @brief 更新激活码（JNI回调）
     */
    void updateActivationCode(const std::string& code);

    /**
     * @brief 获取当前激活码（JNI调用）
     */
    std::string getCurrentActivationCode();

} // namespace ActivationManager
