#include <cstdio>
#include <cstdlib>
#include <vector>
#include <algorithm>
#include <cstring>
#include <string>
#include <csignal>
#include <ctime>
#include <cerrno>
#include <unistd.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <sys/time.h>
#include <sys/stat.h>
#include <thread>
#include <random>
#include <ctime>
#include <regex>
#include <EGL/egl.h>
#include <GLES3/gl3.h>
#include <fstream>
#include <iostream>
#include <filesystem>
#include "imgui.h"
#include "imgui_impl_android.h"
#include "imgui_impl_opengl3.h"
#include "android/native_window_jni.h"
#include "android_native_app_glue.h"
#include "xdl.h"
#include "dobby.h"
#include "format.h"
#include "global.h"
#include "Setup.h"
#include "PackageNames.h"
#include "SysRead.h"
#include "gameHack/XwzcMods.h"
#include <future>
#include <chrono>
#include "UnityResolve.hpp"
#include "Activation.h"
#include <jni.h> // 包含JNI头文件

// 定义全局JavaVM指针
JavaVM* g_jvm = nullptr;


void init() {
    initImGui();
}

std::string packageName = GetPackageName();
std::string xwzcPackageName = GamePackageNames::XWZC;
void _main() {
    if (strcmp(GetPackageName(), GamePackageNames::XWZC) == 0) {
        XwzcMods::Draw();
    }
}

JNIEXPORT jint JNICALL JNI_OnLoad(JavaVM *vm, void *reserved) {
    g_jvm = vm; // 保存JavaVM指针
    LOGI("JNI_OnLoad called, g_jvm set: %p", g_jvm); // 添加日志
    static std::once_flag flag;

    if (strcmp(GetPackageName(), GamePackageNames::XWZC) == 0) {
        std::call_once(flag, []() {
            std::thread([]() {
                while (GetModuleBase("libil2cpp.so", "") == 0) {
                     std::this_thread::sleep_for(std::chrono::seconds(1));
                }
                init();
            }).detach();
        });
    }
    return JNI_VERSION_1_6;
}
