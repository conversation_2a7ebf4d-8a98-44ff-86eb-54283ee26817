#include <jni.h>
#include <android/log.h>
#include "gameHack/ActivationManager.h"

// 全局JavaVM指针
static JavaVM* g_cached_jvm = nullptr;

// 函数声明
void initJNIBridge(JavaVM* vm);

extern "C" {

/**
 * JNI初始化，设置JavaVM
 */
JNIEXPORT void JNICALL
Java_com_example_xposedimgui_InputHelper_setJavaVM(JNIEnv *env, jclass clazz, jlong jvm_ptr) {
    JavaVM* jvm = reinterpret_cast<JavaVM*>(jvm_ptr);
    ActivationManager::setJavaVM(jvm);
    __android_log_print(ANDROID_LOG_INFO, "JNI_Bridge", "JavaVM set from Java");
}

/**
 * 输入完成回调
 */
JNIEXPORT void JNICALL
Java_com_example_xposedimgui_InputHelper_onInputComplete(JNIEnv *env, jclass clazz, jstring input_text) {
    const char* text = env->GetStringUTFChars(input_text, nullptr);
    
    // 格式化激活码
    std::string input = text;
    std::string formatted = "";
    int count = 0;
    for (char c : input) {
        if (c != '-' && ((c >= '0' && c <= '9') || (c >= 'A' && c <= 'Z'))) {
            if (count > 0 && count % 4 == 0) {
                formatted += "-";
            }
            formatted += c;
            count++;
            if (count >= 16) break; // 最多16个字符
        }
    }
    
    // 更新激活码
    ActivationManager::updateActivationCode(formatted);
    
    env->ReleaseStringUTFChars(input_text, text);
    __android_log_print(ANDROID_LOG_INFO, "JNI_Bridge", "Input complete: %s", formatted.c_str());
}

/**
 * 获取当前激活码
 */
JNIEXPORT jstring JNICALL
Java_com_example_xposedimgui_InputHelper_getCurrentActivationCode(JNIEnv *env, jclass clazz) {
    std::string currentCode = ActivationManager::getCurrentActivationCode();
    return env->NewStringUTF(currentCode.c_str());
}

/**
 * 获取当前JavaVM指针
 */
JNIEXPORT jlong JNICALL
Java_com_example_xposedimgui_InputHelper_getCurrentJavaVM(JNIEnv *env, jclass clazz) {
    if (g_cached_jvm == nullptr) {
        // 从JNIEnv获取JavaVM
        if (env->GetJavaVM(&g_cached_jvm) != JNI_OK) {
            __android_log_print(ANDROID_LOG_ERROR, "JNI_Bridge", "Failed to get JavaVM from JNIEnv");
            return 0;
        }
    }

    __android_log_print(ANDROID_LOG_INFO, "JNI_Bridge", "JavaVM pointer: %p", g_cached_jvm);
    return reinterpret_cast<jlong>(g_cached_jvm);
}

/**
 * 粘贴激活码（从剪贴板）
 */
JNIEXPORT void JNICALL
Java_com_example_xposedimgui_InputHelper_jniPasteActivationCode(JNIEnv *env, jclass clazz) {
    __android_log_print(ANDROID_LOG_INFO, "JNI_Bridge", "Paste activation code requested from C++");
    // 这个方法会被C++调用，然后Java层处理粘贴逻辑
}

} // extern "C"

/**
 * 初始化JNI桥接（由main.cpp的JNI_OnLoad调用）
 */
void initJNIBridge(JavaVM* vm) {
    g_cached_jvm = vm;
    ActivationManager::setJavaVM(vm);
    __android_log_print(ANDROID_LOG_INFO, "JNI_Bridge", "JNI Bridge initialized with JavaVM: %p", vm);
}
