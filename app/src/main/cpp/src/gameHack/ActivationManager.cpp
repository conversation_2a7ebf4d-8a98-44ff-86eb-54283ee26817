#include "gameHack/ActivationManager.h"
#include <fstream>
#include <sstream>
#include <iomanip>
#include <random>
#include <unistd.h>
#include <sys/system_properties.h>
#include <android/log.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <netdb.h>
#include <thread>
#include <future>
#include <algorithm>
#include <jni.h>

namespace ActivationManager {

    //================ 服务器配置 ================//
    const char* SERVER_HOST = "**************";
    const int SERVER_PORT = 3001;
    const int NETWORK_TIMEOUT = 10; // 10秒超时

    //================ 加密配置 ================//
    const char* FIXED_ENCRYPTION_KEY = "XwzcActivation24"; // 16字节固定密钥
    const char* FIXED_ENCRYPTION_IV = "InitVector123456";  // 16字节固定IV

    //================ 全局变量定义 ================//
    ActivationInfo g_activationInfo;
    bool g_showActivationWindow = true;
    char g_inputActivationCode[256] = "";
    std::string g_statusMessage = "";
    ImVec4 g_statusColor = ImVec4(1.0f, 1.0f, 1.0f, 1.0f);
    bool g_networkVerificationInProgress = false;



    //================ Base64编码解码函数 ================//

    /**
     * @brief Base64编码
     */
    std::string Base64Encode(const unsigned char* data, size_t length) {
        const char* chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
        std::string result;

        for (size_t i = 0; i < length; i += 3) {
            uint32_t value = 0;
            for (int j = 0; j < 3; j++) {
                value <<= 8;
                if (i + j < length) {
                    value |= data[i + j];
                }
            }

            for (int j = 0; j < 4; j++) {
                if (i * 4 / 3 + j < (length + 2) / 3 * 4) {
                    result += chars[(value >> (6 * (3 - j))) & 0x3F];
                } else {
                    result += '=';
                }
            }
        }

        return result;
    }

    /**
     * @brief Base64解码
     */
    std::vector<unsigned char> Base64Decode(const std::string& encoded) {
        const char* chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
        std::vector<unsigned char> result;

        for (size_t i = 0; i < encoded.length(); i += 4) {
            uint32_t value = 0;
            for (int j = 0; j < 4; j++) {
                if (i + j < encoded.length() && encoded[i + j] != '=') {
                    const char* pos = strchr(chars, encoded[i + j]);
                    if (pos) {
                        value = (value << 6) | (pos - chars);
                    }
                } else {
                    value <<= 6;
                }
            }

            for (int j = 0; j < 3; j++) {
                if (i * 3 / 4 + j < encoded.length() * 3 / 4) {
                    result.push_back((value >> (8 * (2 - j))) & 0xFF);
                }
            }
        }

        return result;
    }

    //================ JNI调用声明 ================//

    // JNI环境和类引用
    JavaVM* g_jvm = nullptr;
    jclass g_inputHelperClass = nullptr;
    jmethodID g_showInputMethod = nullptr;
    jmethodID g_copyToClipboardMethod = nullptr;

    /**
     * @brief 获取JNI环境
     */
    JNIEnv* getJNIEnv() {
        JNIEnv* env = nullptr;

        // 尝试从当前线程获取JNI环境
        if (g_jvm && g_jvm->GetEnv((void**)&env, JNI_VERSION_1_6) == JNI_OK) {
            return env;
        }

        // 如果失败，尝试附加当前线程
        if (g_jvm && g_jvm->AttachCurrentThread(&env, nullptr) == JNI_OK) {
            return env;
        }

        return nullptr;
    }

    /**
     * @brief 设置JVM指针（从Java层调用）
     */
    void setJavaVM(JavaVM* jvm) {
        g_jvm = jvm;
        __android_log_print(ANDROID_LOG_INFO, "ActivationManager", "JavaVM set successfully");
    }

    /**
     * @brief 简化的JNI初始化
     */
    bool initJNICrypto() {
        JNIEnv* env = getJNIEnv();
        if (!env) return false;

        // 查找InputHelper类
        jclass localClass = env->FindClass("com/example/xposedimgui/InputHelper");
        if (!localClass) {
            __android_log_print(ANDROID_LOG_ERROR, "ActivationManager", "Failed to find InputHelper class");
            return false;
        }

        g_inputHelperClass = (jclass)env->NewGlobalRef(localClass);
        env->DeleteLocalRef(localClass);

        // 获取方法ID
        g_showInputMethod = env->GetStaticMethodID(g_inputHelperClass, "jniShowInputMethod", "(Ljava/lang/String;)V");
        g_copyToClipboardMethod = env->GetStaticMethodID(g_inputHelperClass, "jniCopyToClipboard", "(Ljava/lang/String;)Z");

        if (!g_showInputMethod || !g_copyToClipboardMethod) {
            __android_log_print(ANDROID_LOG_ERROR, "ActivationManager", "Failed to get method IDs");
            return false;
        }

        __android_log_print(ANDROID_LOG_INFO, "ActivationManager", "JNI initialized successfully");
        return true;
    }

    /**
     * @brief 简化的AES加密（暂时使用简单实现）
     */
    std::string callJavaAESEncrypt(const std::string& plaintext) {
        // 暂时使用简单的Base64编码作为占位符
        // 在实际运行时，如果需要真正的AES加密，可以通过其他方式实现
        __android_log_print(ANDROID_LOG_INFO, "ActivationManager", "Encrypting data (simplified): %s", plaintext.c_str());
        return Base64Encode((unsigned char*)plaintext.c_str(), plaintext.length());
    }

    /**
     * @brief 简化的AES解密（暂时使用简单实现）
     */
    std::string callJavaAESDecrypt(const std::string& ciphertext) {
        // 暂时使用简单的Base64解码作为占位符
        __android_log_print(ANDROID_LOG_INFO, "ActivationManager", "Decrypting data (simplified): %s", ciphertext.c_str());
        std::vector<unsigned char> decoded = Base64Decode(ciphertext);
        return std::string((char*)decoded.data(), decoded.size());
    }

    /**
     * @brief 调用Java层的剪贴板功能
     */
    bool callJavaClipboard(const std::string& text) {
        JNIEnv* env = getJNIEnv();
        if (!env || !g_inputHelperClass || !g_copyToClipboardMethod) {
            __android_log_print(ANDROID_LOG_ERROR, "ActivationManager", "JNI not initialized for clipboard");
            return false;
        }

        jstring jtext = env->NewStringUTF(text.c_str());
        jboolean result = env->CallStaticBooleanMethod(g_inputHelperClass, g_copyToClipboardMethod, jtext);
        env->DeleteLocalRef(jtext);

        __android_log_print(ANDROID_LOG_INFO, "ActivationManager", "Clipboard copy result: %s", result ? "success" : "failed");
        return result;
    }

    /**
     * @brief 调用Java层显示输入法
     */
    void callJavaShowInput(const std::string& currentText) {
        JNIEnv* env = getJNIEnv();
        if (!env || !g_inputHelperClass || !g_showInputMethod) {
            __android_log_print(ANDROID_LOG_ERROR, "ActivationManager", "JNI not initialized for input method");
            return;
        }

        jstring jtext = env->NewStringUTF(currentText.c_str());
        env->CallStaticVoidMethod(g_inputHelperClass, g_showInputMethod, jtext);
        env->DeleteLocalRef(jtext);

        __android_log_print(ANDROID_LOG_INFO, "ActivationManager", "Input method show requested");
    }

    //================ 加密解密函数 ================//

    /**
     * @brief AES-128-CBC加密（与后端Node.js crypto兼容）
     */
    std::string AESEncrypt(const std::string& plaintext) {
        try {
            // 使用Java加密API通过JNI调用
            return callJavaAESEncrypt(plaintext);
        } catch (...) {
            __android_log_print(ANDROID_LOG_ERROR, "ActivationManager", "AES encryption failed");
            return "";
        }
    }

    /**
     * @brief AES-128-CBC解密（与后端Node.js crypto兼容）
     */
    std::string AESDecrypt(const std::string& ciphertext_b64) {
        try {
            // 使用Java解密API通过JNI调用
            return callJavaAESDecrypt(ciphertext_b64);
        } catch (...) {
            __android_log_print(ANDROID_LOG_ERROR, "ActivationManager", "AES decryption failed");
            return "";
        }
    }

    //================ JSON解析函数 ================//

    /**
     * @brief 解析JSON响应中的字段
     * @param json JSON字符串
     * @param key 要提取的键
     * @return 对应的值，失败返回空字符串
     */
    std::string ParseJsonField(const std::string& json, const std::string& key) {
        std::string searchKey = "\"" + key + "\":";
        size_t keyPos = json.find(searchKey);
        if (keyPos == std::string::npos) return "";

        size_t valueStart = keyPos + searchKey.length();

        // 跳过空格
        while (valueStart < json.length() && json[valueStart] == ' ') valueStart++;

        if (valueStart >= json.length()) return "";

        std::string value;
        if (json[valueStart] == '"') {
            // 字符串值
            valueStart++; // 跳过开始的引号
            size_t valueEnd = json.find('"', valueStart);
            if (valueEnd != std::string::npos) {
                value = json.substr(valueStart, valueEnd - valueStart);
            }
        } else {
            // 数字或布尔值
            size_t valueEnd = json.find_first_of(",}", valueStart);
            if (valueEnd != std::string::npos) {
                value = json.substr(valueStart, valueEnd - valueStart);
                // 移除尾部空格
                while (!value.empty() && value.back() == ' ') value.pop_back();
            }
        }

        return value;
    }

    //================ 网络通信函数 ================//

    /**
     * @brief 发送加密HTTP POST请求到服务器
     * @param endpoint API端点
     * @param jsonData JSON数据
     * @return 服务器响应，失败返回空字符串
     */
    std::string SendHttpRequest(const std::string& endpoint, const std::string& jsonData) {
        // 加密JSON数据
        std::string encryptedData = AESEncrypt(jsonData);
        if (encryptedData.empty()) {
            __android_log_print(ANDROID_LOG_ERROR, "ActivationManager", "Failed to encrypt data");
            return "";
        }

        // 构建加密请求的JSON包装
        std::stringstream encryptedJson;
        encryptedJson << "{\"encrypted\":\"" << encryptedData << "\"}";
        std::string requestData = encryptedJson.str();
        int sockfd = socket(AF_INET, SOCK_STREAM, 0);
        if (sockfd < 0) {
            __android_log_print(ANDROID_LOG_ERROR, "ActivationManager", "Failed to create socket");
            return "";
        }

        // 设置超时
        struct timeval timeout;
        timeout.tv_sec = NETWORK_TIMEOUT;
        timeout.tv_usec = 0;
        setsockopt(sockfd, SOL_SOCKET, SO_RCVTIMEO, &timeout, sizeof(timeout));
        setsockopt(sockfd, SOL_SOCKET, SO_SNDTIMEO, &timeout, sizeof(timeout));

        // 解析服务器地址
        struct sockaddr_in server_addr;
        memset(&server_addr, 0, sizeof(server_addr));
        server_addr.sin_family = AF_INET;
        server_addr.sin_port = htons(SERVER_PORT);

        if (inet_pton(AF_INET, SERVER_HOST, &server_addr.sin_addr) <= 0) {
            __android_log_print(ANDROID_LOG_ERROR, "ActivationManager", "Invalid server address");
            close(sockfd);
            return "";
        }

        // 连接服务器
        if (connect(sockfd, (struct sockaddr*)&server_addr, sizeof(server_addr)) < 0) {
            __android_log_print(ANDROID_LOG_ERROR, "ActivationManager", "Failed to connect to server");
            close(sockfd);
            return "";
        }

        // 构建HTTP请求
        std::stringstream request;
        request << "POST " << endpoint << " HTTP/1.1\r\n";
        request << "Host: " << SERVER_HOST << ":" << SERVER_PORT << "\r\n";
        request << "Content-Type: application/json\r\n";
        request << "Content-Length: " << requestData.length() << "\r\n";
        request << "X-Encryption: AES-128-CBC\r\n"; // 标识使用加密
        request << "Connection: close\r\n";
        request << "\r\n";
        request << requestData;

        std::string requestStr = request.str();

        // 发送请求
        if (send(sockfd, requestStr.c_str(), requestStr.length(), 0) < 0) {
            __android_log_print(ANDROID_LOG_ERROR, "ActivationManager", "Failed to send request");
            close(sockfd);
            return "";
        }

        // 接收响应
        std::string response;
        char buffer[4096];
        ssize_t bytes_received;

        while ((bytes_received = recv(sockfd, buffer, sizeof(buffer) - 1, 0)) > 0) {
            buffer[bytes_received] = '\0';
            response += buffer;
        }

        close(sockfd);

        // 提取JSON响应体（跳过HTTP头）
        size_t body_start = response.find("\r\n\r\n");
        std::string responseBody;
        if (body_start != std::string::npos) {
            responseBody = response.substr(body_start + 4);
        } else {
            responseBody = response;
        }

        // 尝试解密响应
        std::string encryptedField = ParseJsonField(responseBody, "encrypted");
        if (!encryptedField.empty()) {
            // 响应是加密的，进行解密
            std::string decryptedResponse = AESDecrypt(encryptedField);
            if (!decryptedResponse.empty()) {
                __android_log_print(ANDROID_LOG_INFO, "ActivationManager",
                                   "Decrypted response: %s", decryptedResponse.c_str());
                return decryptedResponse;
            } else {
                __android_log_print(ANDROID_LOG_ERROR, "ActivationManager", "Failed to decrypt response");
                return "";
            }
        }

        // 响应未加密，直接返回
        return responseBody;
    }



    //================ 内部函数 ================//
    
    /**
     * @brief 获取Android设备属性
     */
    std::string GetAndroidProperty(const char* key) {
        char value[PROP_VALUE_MAX] = {0};
        __system_property_get(key, value);
        return std::string(value);
    }

    /**
     * @brief 获取设备序列号
     */
    std::string GetDeviceSerial() {
        std::string serial = GetAndroidProperty("ro.serialno");
        if (serial.empty()) {
            serial = GetAndroidProperty("ro.boot.serialno");
        }
        return serial;
    }

    /**
     * @brief 获取设备型号和品牌
     */
    std::string GetDeviceInfo() {
        std::string brand = GetAndroidProperty("ro.product.brand");
        std::string model = GetAndroidProperty("ro.product.model");
        std::string board = GetAndroidProperty("ro.product.board");

        return brand + "-" + model + "-" + board;
    }

    /**
     * @brief 简单的字符串哈希
     */
    uint32_t SimpleHash(const std::string& str) {
        uint32_t hash = 5381;
        for (char c : str) {
            hash = ((hash << 5) + hash) + c;
        }
        return hash;
    }

    //================ 公共函数实现 ================//

    void Init() {
        // 初始化JNI加密环境
        if (!initJNICrypto()) {
            __android_log_print(ANDROID_LOG_WARN, "ActivationManager", "Failed to init JNI crypto, encryption may not work");
        }

        g_activationInfo.deviceId = GenerateDeviceId();
        LoadActivationInfo();

        // 检查本地激活状态
        g_activationInfo.status = CheckActivationStatus();

        if (g_activationInfo.status == ActivationStatus::Activated) {
            g_showActivationWindow = false;
            g_statusMessage = "设备已激活，正在验证...";
            g_statusColor = ImVec4(0.0f, 1.0f, 0.0f, 1.0f);

            // 进行网络验证
            VerifyActivationOnline(false);
        } else {
            g_showActivationWindow = true;
            g_statusMessage = "请输入激活码激活设备";
            g_statusColor = ImVec4(1.0f, 0.5f, 0.0f, 1.0f);
        }
    }

    std::string GenerateDeviceId() {
        std::string serial = GetDeviceSerial();
        std::string deviceInfo = GetDeviceInfo();

        // 组合设备序列号和设备信息生成设备ID
        std::string combined = serial + deviceInfo;
        uint32_t hash = SimpleHash(combined);

        std::stringstream ss;
        ss << "AND-" << std::hex << std::uppercase << hash;

        return ss.str();
    }

    bool ValidateActivationCode(const std::string& activationCode) {
        // 简单的激活码验证逻辑
        // 格式: XXXX-XXXX-XXXX-XXXX (16个字符，不包括连字符)
        if (activationCode.length() != 19) return false;
        
        // 检查格式
        if (activationCode[4] != '-' || activationCode[9] != '-' || 
            activationCode[14] != '-') return false;
        
        // 检查是否都是字母数字
        for (int i = 0; i < 19; i++) {
            if (i == 4 || i == 9 || i == 14) continue;
            char c = activationCode[i];
            if (!((c >= '0' && c <= '9') || (c >= 'A' && c <= 'Z'))) {
                return false;
            }
        }
        
        return true;
    }

    bool ActivateDevice(const std::string& activationCode) {
        if (!ValidateActivationCode(activationCode)) {
            g_statusMessage = "激活码格式错误";
            g_statusColor = ImVec4(1.0f, 0.0f, 0.0f, 1.0f);
            return false;
        }

        if (g_networkVerificationInProgress) {
            g_statusMessage = "网络验证进行中，请稍候...";
            g_statusColor = ImVec4(1.0f, 1.0f, 0.0f, 1.0f);
            return false;
        }

        // 异步进行网络验证
        g_networkVerificationInProgress = true;
        g_statusMessage = "正在连接服务器验证...";
        g_statusColor = ImVec4(0.0f, 0.5f, 1.0f, 1.0f);

        // 在后台线程进行网络验证
        std::thread([activationCode]() {
            try {
                // 构建验证请求JSON
                std::stringstream jsonRequest;
                jsonRequest << "{"
                           << "\"action\":\"activate\","
                           << "\"deviceId\":\"" << g_activationInfo.deviceId << "\","
                           << "\"activationCode\":\"" << activationCode << "\","
                           << "\"platform\":\"android\""
                           << "}";

                __android_log_print(ANDROID_LOG_INFO, "ActivationManager",
                                   "Sending encrypted activation request");
                __android_log_print(ANDROID_LOG_DEBUG, "ActivationManager",
                                   "Original JSON: %s", jsonRequest.str().c_str());

                // 发送验证请求
                std::string response = SendHttpRequest("/api/activate", jsonRequest.str());

                if (response.empty()) {
                    g_statusMessage = "网络连接失败，请检查网络";
                    g_statusColor = ImVec4(1.0f, 0.0f, 0.0f, 1.0f);
                    g_networkVerificationInProgress = false;
                    return;
                }

                __android_log_print(ANDROID_LOG_INFO, "ActivationManager",
                                   "Server response: %s", response.c_str());

                // 解析服务器响应
                std::string success = ParseJsonField(response, "success");
                std::string message = ParseJsonField(response, "message");
                std::string expireDays = ParseJsonField(response, "expireDays");
                std::string userName = ParseJsonField(response, "userName");

                if (success == "true") {
                    // 激活成功
                    g_activationInfo.activationCode = activationCode;
                    g_activationInfo.activatedTime = std::chrono::system_clock::now();

                    // 设置过期时间
                    int days = 30; // 默认30天
                    if (!expireDays.empty()) {
                        days = std::stoi(expireDays);
                    }
                    g_activationInfo.expireTime = g_activationInfo.activatedTime + std::chrono::hours(24 * days);

                    g_activationInfo.status = ActivationStatus::Activated;
                    g_activationInfo.userName = userName.empty() ? "User" : userName;

                    SaveActivationInfo();

                    g_statusMessage = message.empty() ? "激活成功！" : message;
                    g_statusColor = ImVec4(0.0f, 1.0f, 0.0f, 1.0f);
                    g_showActivationWindow = false;

                } else {
                    // 激活失败
                    g_statusMessage = message.empty() ? "激活失败，请检查激活码" : message;
                    g_statusColor = ImVec4(1.0f, 0.0f, 0.0f, 1.0f);
                }

            } catch (const std::exception& e) {
                __android_log_print(ANDROID_LOG_ERROR, "ActivationManager",
                                   "Activation error: %s", e.what());
                g_statusMessage = "激活过程中发生错误";
                g_statusColor = ImVec4(1.0f, 0.0f, 0.0f, 1.0f);
            }

            g_networkVerificationInProgress = false;
        }).detach();

        return true; // 返回true表示验证请求已发送
    }

    ActivationStatus CheckActivationStatus() {
        if (g_activationInfo.activationCode.empty()) {
            return ActivationStatus::NotActivated;
        }
        
        auto now = std::chrono::system_clock::now();
        if (now > g_activationInfo.expireTime) {
            return ActivationStatus::Expired;
        }
        
        return ActivationStatus::Activated;
    }

    void SaveActivationInfo() {
        std::ofstream file("activation.dat", std::ios::binary);
        if (file.is_open()) {
            // 简单的二进制保存
            size_t len = g_activationInfo.deviceId.length();
            file.write(reinterpret_cast<const char*>(&len), sizeof(len));
            file.write(g_activationInfo.deviceId.c_str(), len);
            
            len = g_activationInfo.activationCode.length();
            file.write(reinterpret_cast<const char*>(&len), sizeof(len));
            file.write(g_activationInfo.activationCode.c_str(), len);
            
            len = g_activationInfo.userName.length();
            file.write(reinterpret_cast<const char*>(&len), sizeof(len));
            file.write(g_activationInfo.userName.c_str(), len);
            
            auto activatedTime = g_activationInfo.activatedTime.time_since_epoch().count();
            file.write(reinterpret_cast<const char*>(&activatedTime), sizeof(activatedTime));
            
            auto expireTime = g_activationInfo.expireTime.time_since_epoch().count();
            file.write(reinterpret_cast<const char*>(&expireTime), sizeof(expireTime));
            
            file.close();
        }
    }

    void LoadActivationInfo() {
        std::ifstream file("activation.dat", std::ios::binary);
        if (file.is_open()) {
            try {
                size_t len;
                
                file.read(reinterpret_cast<char*>(&len), sizeof(len));
                g_activationInfo.deviceId.resize(len);
                file.read(&g_activationInfo.deviceId[0], len);
                
                file.read(reinterpret_cast<char*>(&len), sizeof(len));
                g_activationInfo.activationCode.resize(len);
                file.read(&g_activationInfo.activationCode[0], len);
                
                file.read(reinterpret_cast<char*>(&len), sizeof(len));
                g_activationInfo.userName.resize(len);
                file.read(&g_activationInfo.userName[0], len);
                
                long long activatedTime;
                file.read(reinterpret_cast<char*>(&activatedTime), sizeof(activatedTime));
                g_activationInfo.activatedTime = std::chrono::system_clock::time_point(
                    std::chrono::system_clock::duration(activatedTime));
                
                long long expireTime;
                file.read(reinterpret_cast<char*>(&expireTime), sizeof(expireTime));
                g_activationInfo.expireTime = std::chrono::system_clock::time_point(
                    std::chrono::system_clock::duration(expireTime));
                
            } catch (...) {
                // 文件损坏，重置激活信息
                g_activationInfo = ActivationInfo();
                g_activationInfo.deviceId = GenerateDeviceId();
            }
            file.close();
        }
    }

    bool IsActivated() {
        return CheckActivationStatus() == ActivationStatus::Activated;
    }

    int GetRemainingDays() {
        if (!IsActivated()) return -1;
        
        auto now = std::chrono::system_clock::now();
        auto remaining = g_activationInfo.expireTime - now;
        auto days = std::chrono::duration_cast<std::chrono::hours>(remaining).count() / 24;
        
        return static_cast<int>(days);
    }

    std::string FormatTime(const std::chrono::system_clock::time_point& timePoint) {
        auto time_t = std::chrono::system_clock::to_time_t(timePoint);
        std::stringstream ss;
        ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
        return ss.str();
    }

    void CopyToClipboard(const std::string& text) {
        // 调用Java层的剪贴板功能
        if (callJavaClipboard(text)) {
            g_statusMessage = "设备ID已复制到剪贴板";
            g_statusColor = ImVec4(0.0f, 1.0f, 0.0f, 1.0f);
        } else {
            g_statusMessage = "复制失败，设备ID: " + text;
            g_statusColor = ImVec4(1.0f, 0.5f, 0.0f, 1.0f);
        }
        __android_log_print(ANDROID_LOG_INFO, "ActivationManager", "Copy to clipboard: %s", text.c_str());
    }

    void VerifyActivationOnline(bool silent) {
        if (g_networkVerificationInProgress) return;

        if (g_activationInfo.activationCode.empty()) {
            if (!silent) {
                g_statusMessage = "未找到激活码";
                g_statusColor = ImVec4(1.0f, 0.0f, 0.0f, 1.0f);
            }
            return;
        }

        g_networkVerificationInProgress = true;
        if (!silent) {
            g_statusMessage = "正在验证激活状态...";
            g_statusColor = ImVec4(0.0f, 0.5f, 1.0f, 1.0f);
        }

        // 在后台线程进行网络验证
        std::thread([silent]() {
            try {
                // 构建验证请求JSON
                std::stringstream jsonRequest;
                jsonRequest << "{"
                           << "\"action\":\"verify\","
                           << "\"deviceId\":\"" << g_activationInfo.deviceId << "\","
                           << "\"activationCode\":\"" << g_activationInfo.activationCode << "\""
                           << "}";

                // 发送验证请求
                std::string response = SendHttpRequest("/api/verify", jsonRequest.str());

                if (response.empty()) {
                    if (!silent) {
                        g_statusMessage = "网络验证失败，使用本地验证";
                        g_statusColor = ImVec4(1.0f, 0.5f, 0.0f, 1.0f);
                    }
                    g_networkVerificationInProgress = false;
                    return;
                }

                // 解析服务器响应
                std::string success = ParseJsonField(response, "success");
                std::string message = ParseJsonField(response, "message");
                std::string isActive = ParseJsonField(response, "isActive");
                std::string expireDays = ParseJsonField(response, "expireDays");

                if (success == "true") {
                    if (isActive == "true") {
                        // 激活状态有效
                        g_activationInfo.status = ActivationStatus::Activated;

                        // 更新过期时间
                        if (!expireDays.empty()) {
                            int days = std::stoi(expireDays);
                            g_activationInfo.expireTime = std::chrono::system_clock::now() + std::chrono::hours(24 * days);
                            SaveActivationInfo();
                        }

                        if (!silent) {
                            g_statusMessage = "激活状态验证成功";
                            g_statusColor = ImVec4(0.0f, 1.0f, 0.0f, 1.0f);
                        }
                    } else {
                        // 激活已失效
                        g_activationInfo.status = ActivationStatus::Expired;
                        if (!silent) {
                            g_statusMessage = message.empty() ? "激活已失效，请重新激活" : message;
                            g_statusColor = ImVec4(1.0f, 0.0f, 0.0f, 1.0f);
                            g_showActivationWindow = true;
                        }
                    }
                } else {
                    if (!silent) {
                        g_statusMessage = message.empty() ? "服务器验证失败" : message;
                        g_statusColor = ImVec4(1.0f, 0.0f, 0.0f, 1.0f);
                    }
                }

            } catch (const std::exception& e) {
                __android_log_print(ANDROID_LOG_ERROR, "ActivationManager",
                                   "Verification error: %s", e.what());
                if (!silent) {
                    g_statusMessage = "验证过程中发生错误";
                    g_statusColor = ImVec4(1.0f, 0.0f, 0.0f, 1.0f);
                }
            }

            g_networkVerificationInProgress = false;
        }).detach();
    }

    void DrawActivationUI() {
        if (!g_showActivationWindow && IsActivated()) {
            // 已激活状态下的简单状态显示
            ImGui::Begin("激活状态", nullptr, ImGuiWindowFlags_AlwaysAutoResize | ImGuiWindowFlags_NoCollapse);

            ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), "✓ 设备已激活");
            ImGui::Text("用户: %s", g_activationInfo.userName.c_str());
            ImGui::Text("剩余天数: %d 天", GetRemainingDays());
            ImGui::Text("到期时间: %s", FormatTime(g_activationInfo.expireTime).c_str());

            if (ImGui::Button("重新激活")) {
                g_showActivationWindow = true;
            }

            ImGui::SameLine();
            if (ImGui::Button("验证状态")) {
                VerifyActivationOnline(false);
            }

            ImGui::End();
            return;
        }

        // 激活窗口
        ImGui::Begin("设备激活", &g_showActivationWindow,
                     ImGuiWindowFlags_AlwaysAutoResize |
                     ImGuiWindowFlags_NoCollapse |
                     ImGuiWindowFlags_NoResize);

        // 标题
        ImGui::PushFont(nullptr); // 使用默认字体，可以替换为大字体
        ImGui::TextColored(ImVec4(0.2f, 0.8f, 1.0f, 1.0f), "设备激活验证");
        ImGui::PopFont();
        ImGui::Separator();

        // 设备ID显示
        ImGui::Text("设备ID:");

        // 设备ID显示框
        ImGui::PushStyleColor(ImGuiCol_FrameBg, ImVec4(0.2f, 0.2f, 0.2f, 1.0f));
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(1.0f, 1.0f, 0.0f, 1.0f));
        ImGui::SetNextItemWidth(250);
        char deviceIdBuffer[64];
        strncpy(deviceIdBuffer, g_activationInfo.deviceId.c_str(), sizeof(deviceIdBuffer) - 1);
        deviceIdBuffer[sizeof(deviceIdBuffer) - 1] = '\0';
        ImGui::InputText("##device_id_display", deviceIdBuffer, sizeof(deviceIdBuffer), ImGuiInputTextFlags_ReadOnly);
        ImGui::PopStyleColor(2);

        ImGui::SameLine();
        ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.0f, 0.7f, 0.0f, 1.0f));
        ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0.0f, 0.8f, 0.0f, 1.0f));
        if (ImGui::Button("📋 复制", ImVec2(60, 0))) {
            CopyToClipboard(g_activationInfo.deviceId);
        }
        ImGui::PopStyleColor(2);

        ImGui::Spacing();
        ImGui::TextWrapped("请将上述设备ID发送给管理员进行绑定，然后输入获得的激活码：");
        ImGui::Spacing();

        // 激活码输入
        ImGui::Text("激活码:");
        ImGui::SameLine();
        ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "(格式: XXXX-XXXX-XXXX-XXXX)");

        ImGui::SetNextItemWidth(250);

        // 使用按钮来触发输入法
        ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.2f, 0.2f, 0.2f, 1.0f));
        ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0.3f, 0.3f, 0.3f, 1.0f));
        ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(1.0f, 1.0f, 1.0f, 1.0f));

        std::string displayText = strlen(g_inputActivationCode) > 0 ? g_inputActivationCode : "点击输入激活码...";
        if (ImGui::Button(displayText.c_str(), ImVec2(250, 30))) {
            // 调用输入法
            callJavaShowInput(g_inputActivationCode);
            __android_log_print(ANDROID_LOG_INFO, "ActivationManager", "Input method requested for activation code");
        }
        ImGui::PopStyleColor(3);

        // 添加粘贴和清空按钮
        ImGui::SameLine();
        if (ImGui::Button("粘贴", ImVec2(50, 0))) {
            __android_log_print(ANDROID_LOG_INFO, "ActivationManager", "Paste button clicked");
            g_statusMessage = "粘贴功能开发中...";
            g_statusColor = ImVec4(1.0f, 0.5f, 0.0f, 1.0f);
        }

        ImGui::SameLine();
        if (ImGui::Button("清空", ImVec2(50, 0))) {
            memset(g_inputActivationCode, 0, sizeof(g_inputActivationCode));
            g_statusMessage = "激活码已清空";
            g_statusColor = ImVec4(0.0f, 1.0f, 0.0f, 1.0f);
        }

        ImGui::Spacing();

        // 激活按钮
        bool canActivate = strlen(g_inputActivationCode) >= 19 && !g_networkVerificationInProgress; // XXXX-XXXX-XXXX-XXXX
        if (!canActivate) {
            ImGui::PushStyleVar(ImGuiStyleVar_Alpha, 0.5f);
        }

        if (g_networkVerificationInProgress) {
            ImGui::Button("网络验证中...", ImVec2(120, 30));
        } else {
            if (ImGui::Button("激活设备", ImVec2(120, 30)) && canActivate) {
                ActivateDevice(g_inputActivationCode);
            }
        }

        if (!canActivate) {
            ImGui::PopStyleVar();
        }

        ImGui::SameLine();
        if (ImGui::Button("清空", ImVec2(60, 30))) {
            memset(g_inputActivationCode, 0, sizeof(g_inputActivationCode));
        }

        // 状态消息
        if (!g_statusMessage.empty()) {
            ImGui::Spacing();
            ImGui::TextColored(g_statusColor, "%s", g_statusMessage.c_str());
        }

        // 帮助信息
        ImGui::Spacing();
        ImGui::Separator();
        ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "激活码格式: XXXX-XXXX-XXXX-XXXX");
        ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "如有问题请联系管理员");
        ImGui::TextColored(ImVec4(0.5f, 1.0f, 0.5f, 1.0f), "🔒 通信已加密保护");

        // 当前激活状态
        if (g_activationInfo.status != ActivationStatus::NotActivated) {
            ImGui::Spacing();
            ImGui::Separator();
            ImGui::Text("当前状态:");

            switch (g_activationInfo.status) {
                case ActivationStatus::Activated:
                    ImGui::SameLine();
                    ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), "已激活");
                    ImGui::Text("剩余: %d 天", GetRemainingDays());
                    break;
                case ActivationStatus::Expired:
                    ImGui::SameLine();
                    ImGui::TextColored(ImVec4(1.0f, 0.0f, 0.0f, 1.0f), "已过期");
                    break;
                case ActivationStatus::Invalid:
                    ImGui::SameLine();
                    ImGui::TextColored(ImVec4(1.0f, 0.5f, 0.0f, 1.0f), "无效");
                    break;
                default:
                    break;
            }
        }

        ImGui::End();
    }

    void updateActivationCode(const std::string& code) {
        strncpy(g_inputActivationCode, code.c_str(), sizeof(g_inputActivationCode) - 1);
        g_inputActivationCode[sizeof(g_inputActivationCode) - 1] = '\0';
        __android_log_print(ANDROID_LOG_INFO, "ActivationManager", "Activation code updated: %s", code.c_str());
    }

    std::string getCurrentActivationCode() {
        return std::string(g_inputActivationCode);
    }

} // namespace ActivationManager
