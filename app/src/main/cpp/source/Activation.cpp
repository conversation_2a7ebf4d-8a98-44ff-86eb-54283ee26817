#include "Activation.h"
#include "libs/httplib.h"
#include "libs/json.hpp"
#include <thread>
#include <fstream>
#include <vector>
#include <cstring>
#include <jni.h> // 1. 包含JNI头文件
#include <functional> // 2. 包含函数对象头文件
#include <mutex> // 3. 包含互斥锁头文件
#include "Setup.h" // For LOGI

// 4. 引用在main.cpp中定义的全局JavaVM指针
extern JavaVM* g_jvm;

// 引用 XwzcMods::DoMainInit
namespace XwzcMods { void DoMainInit(); }

// Forward declare for aes.c, as it's a C-style library
extern "C" {
#include "libs/aes.h"
}

// 1. 先声明一个命名空间内的辅助函数
namespace Activation {
    void set_activation_code(const char* code);
}

// 2. JNI 回调函数，现在调用辅助函数
extern "C" JNIEXPORT void JNICALL
Java_com_hook_xposedimgui_Core_setKeyboardResult(JNIEnv* env, jclass, jstring result) {
    const char* str = env->GetStringUTFChars(result, 0);
    Activation::set_activation_code(str);
    env->ReleaseStringUTFChars(result, str);
}


using json = nlohmann::json;

//================ 命名空间和常量 ================//
namespace Activation {

    namespace Config {
        const char* SERVER_HOST = "**************";
        const int SERVER_PORT = 3001;
        // 使用字符列表初始化，以避免字符串末尾的空终止符'\0'导致数组溢出
        const uint8_t ENCRYPTION_KEY[16] = { 'X','w','z','c','A','c','t','i','v','a','t','i','o','n','2','4' };
        const uint8_t ENCRYPTION_IV[16]  = { 'I','n','i','t','V','e','c','t','o','r','1','2','3','4','5','6' };
    }

    //================ 全局变量定义 ================//
    Status g_status = Status::NotInitialized;
    bool g_is_activated = false;
    std::string g_user_name = "未激活";
    int g_expire_days = 0;
    
    // 私有全局变量
    static std::string g_activation_code(20, '\0');
    static std::string g_device_id = "";
    static std::string g_message = "请输入激活码进行验证。";
    static bool g_is_verifying = false;

    // 任务队列实现
    static std::vector<std::function<void()>> g_post_activation_tasks;
    static std::mutex g_tasks_mutex;

    void AddPostActivationTask(const std::function<void()>& task) {
        std::lock_guard<std::mutex> lock(g_tasks_mutex);
        g_post_activation_tasks.push_back(task);
    }

    void ExecutePostActivationTasks() {
        std::vector<std::function<void()>> tasks_to_run;
        {
            std::lock_guard<std::mutex> lock(g_tasks_mutex);
            if (!g_post_activation_tasks.empty()) {
                tasks_to_run.swap(g_post_activation_tasks);
            }
        }
        for (const auto& task : tasks_to_run) {
            task();
        }
    }

    //================ 辅助函数 (Base64 & AES) ================//

    // Base64 编解码 (标准实现)
    std::string base64_encode(const std::string& in);
    std::string base64_decode(const std::string& in);

    std::string aes_encrypt_cbc(const std::string& plain_text) {
        std::string padded_text = plain_text;
        size_t padding = 16 - (padded_text.length() % 16);
        padded_text.append(padding, (char)padding);

        std::vector<uint8_t> buffer(padded_text.begin(), padded_text.end());
        
        AES_ctx ctx;
        uint8_t iv[16];
        memcpy(iv, Config::ENCRYPTION_IV, 16);
        AES_init_ctx_iv(&ctx, Config::ENCRYPTION_KEY, iv);
        AES_CBC_encrypt_buffer(&ctx, buffer.data(), buffer.size());

        return base64_encode(std::string(buffer.begin(), buffer.end()));
    }

    std::string aes_decrypt_cbc(const std::string& b64_cipher_text) {
        std::string cipher_text = base64_decode(b64_cipher_text);
        std::vector<uint8_t> buffer(cipher_text.begin(), cipher_text.end());

        AES_ctx ctx;
        uint8_t iv[16];
        memcpy(iv, Config::ENCRYPTION_IV, 16);
        AES_init_ctx_iv(&ctx, Config::ENCRYPTION_KEY, iv);
        AES_CBC_decrypt_buffer(&ctx, buffer.data(), buffer.size());
        
        size_t padding = buffer.back();
        if (padding > 0 && padding <= 16) {
            return std::string(buffer.begin(), buffer.end() - padding);
        }
        return std::string(buffer.begin(), buffer.end());
    }


    //================ 核心功能实现 ================//

    // JNI 帮助函数 - 获取Application Context
    jobject get_application_context() {
        if (!g_jvm) { LOGI("get_application_context: g_jvm is null"); return nullptr; }

        JNIEnv* env = nullptr;
        g_jvm->AttachCurrentThread(&env, nullptr);
        if (!env) return nullptr;

        jclass activity_thread_clz = env->FindClass("android/app/ActivityThread");
        if (!activity_thread_clz) return nullptr;

        jmethodID current_application_mid = env->GetStaticMethodID(activity_thread_clz, "currentApplication", "()Landroid/app/Application;");
        if (!current_application_mid) return nullptr;

        jobject application = env->CallStaticObjectMethod(activity_thread_clz, current_application_mid);
        LOGI("get_application_context: success, application: %p", application);
        return application;
    }

    std::string get_device_id() {
        LOGI("get_device_id: starting...");
        if (!g_jvm) { LOGI("get_device_id: g_jvm is null"); return "NO_JVM"; }

        JNIEnv* env = nullptr;
        if (g_jvm->AttachCurrentThread(&env, nullptr) != JNI_OK) { LOGI("get_device_id: AttachCurrentThread failed"); return "NO_ENV"; }
        if (!env) { LOGI("get_device_id: env is null"); return "NO_ENV"; }
        
        jobject context = get_application_context();
        if (!context) { LOGI("get_device_id: context is null"); return "NO_CONTEXT"; }
        
        jclass core_clz = env->FindClass("com/hook/xposedimgui/Core");
        if (!core_clz) { LOGI("get_device_id: FindClass failed for Core"); return "NO_CORE_CLASS"; }

        jmethodID get_device_id_mid = env->GetStaticMethodID(core_clz, "getDeviceID", "(Landroid/content/Context;)Ljava/lang/String;");
        if (!get_device_id_mid) { LOGI("get_device_id: GetStaticMethodID failed for getDeviceID"); return "NO_METHOD"; }

        jstring j_device_id = (jstring)env->CallStaticObjectMethod(core_clz, get_device_id_mid, context);
        if (!j_device_id) { LOGI("get_device_id: CallStaticObjectMethod returned null"); return "NO_DEVICE_ID"; }
        
        const char* device_id_cstr = env->GetStringUTFChars(j_device_id, nullptr);
        std::string device_id(device_id_cstr);
        env->ReleaseStringUTFChars(j_device_id, device_id_cstr);
        
        LOGI("get_device_id: success, found device_id: %s", device_id.c_str());
        // 返回格式化的ID
        return "AND-" + device_id;
    }

    std::string get_config_file_path() {
        // 在Android上，应用数据目录通常是 /data/data/包名/
        // 为了方便调试，我们暂时使用 /sdcard/
        return "/sdcard/activation_config.json";
    }

    void save_activation_code(const std::string& code) {
        json data;
        data["activation_code"] = code;
        std::ofstream file(get_config_file_path());
        if (file.is_open()) {
            file << data.dump();
        }
    }

    std::string load_activation_code() {
        std::ifstream file(get_config_file_path());
        if (file.is_open()) {
            try {
                json data;
                file >> data;
                if (data.contains("activation_code")) {
                    return data["activation_code"];
                }
            } catch (...) { /* 文件损坏或为空 */ }
        }
        return "";
    }

    void perform_verification(const std::string& action) {
        g_is_verifying = true;
        g_status = Status::Verifying;
        g_message = "正在验证，请稍候...";

        try {
            json request_data;
            request_data["action"] = action;
            request_data["deviceId"] = g_device_id;
            request_data["activationCode"] = std::string(g_activation_code.c_str());
            if (action == "activate") {
                request_data["platform"] = "android";
            }
            
            std::string encrypted_str = aes_encrypt_cbc(request_data.dump());
            json payload;
            payload["encrypted"] = encrypted_str;

            httplib::Client cli(Config::SERVER_HOST, Config::SERVER_PORT);
            cli.set_connection_timeout(10);
            
            auto res = cli.Post(action == "activate" ? "/api/activate" : "/api/verify", payload.dump(), "application/json");

            if (!res) {
                g_status = Status::NetworkError;
                g_message = "网络错误: 无法连接到服务器。";
            } else if (res->status != 200) {
                g_status = Status::Failed;
                g_message = "服务器错误: HTTP " + std::to_string(res->status);
            } else {
                json response_payload = json::parse(res->body);
                std::string decrypted_str = aes_decrypt_cbc(response_payload["encrypted"].get<std::string>());
                json response_data = json::parse(decrypted_str);

                if (response_data.value("success", false) && response_data.value("isActive", false)) {
                    // 验证成功！
                    AddPostActivationTask([response_data]() {
                        g_status = Status::Success;
                        g_is_activated = true;
                        g_expire_days = response_data.value("expireDays", 0);
                        g_user_name = response_data.value("userName", "用户");
                        g_message = "验证成功！剩余 " + std::to_string(g_expire_days) + " 天。";
                        
                        // 在这里触发主功能初始化
                        XwzcMods::DoMainInit();
                    });
                } else {
                    g_status = Status::Failed;
                    g_message = response_data.value("message", "未知错误。");
                }
            }
        } catch (...) {
            g_status = Status::Failed;
            g_message = "处理失败: 发生未知异常。";
        }
        g_is_verifying = false;
    }

    void Init() {
        LOGI("Activation::Init: starting...");
        g_device_id = get_device_id();
        LOGI("Activation::Init: g_device_id set to '%s'", g_device_id.c_str());

        std::string loaded_code = load_activation_code();
        LOGI("Activation::Init: loaded_code: '%s'", loaded_code.c_str());
        if (!loaded_code.empty()) {
            strncpy(&g_activation_code[0], loaded_code.c_str(), g_activation_code.size() - 1);
            std::thread(perform_verification, "verify").detach();
            LOGI("Activation::Init: verification thread started.");
        }
        LOGI("Activation::Init: finished.");
    }

    void DrawUI() {
        if (g_is_activated) return; // 如果已激活，则不显示任何UI

        ImGui::OpenPopup("Activation##Popup");
        ImGui::SetNextWindowSize(ImVec2(500, 300), ImGuiCond_Appearing); // 调整窗口大小
        if (ImGui::BeginPopupModal("Activation##Popup", NULL, ImGuiWindowFlags_NoResize | ImGuiWindowFlags_NoCollapse | ImGuiWindowFlags_NoMove)) {
            ImGui::Text("激活系统");
            ImGui::Separator();
            
            ImGui::Text("设备ID: %s", g_device_id.c_str());

            // --- 新的输入框实现 ---
            ImGui::Text("激活码:");
            ImGui::SameLine();
            if (ImGui::Button(g_activation_code.c_str(), ImVec2(ImGui::GetContentRegionAvail().x, 0))) {
                // 调用 showKeyboard
                JNIEnv* env = nullptr;
                if (g_jvm && g_jvm->AttachCurrentThread(&env, nullptr) == JNI_OK) {
                    jobject context = get_application_context();
                    jclass core_clz = env->FindClass("com/hook/xposedimgui/Core");
                    jmethodID show_keyboard_mid = env->GetStaticMethodID(core_clz, "showKeyboard", "(Landroid/content/Context;Ljava/lang/String;)V");
                    jstring initial_text = env->NewStringUTF(g_activation_code.c_str());

                    if (context && core_clz && show_keyboard_mid) {
                        env->CallStaticVoidMethod(core_clz, show_keyboard_mid, context, initial_text);
                    }
                    env->DeleteLocalRef(initial_text);
                }
            }
            // --- 输入框实现结束 ---
            
            if (ImGui::Button("立即激活", ImVec2(-1, 0))) {
                if (!g_is_verifying) {
                    save_activation_code(std::string(g_activation_code.c_str()));
                    std::thread(perform_verification, "activate").detach();
                }
            }

            ImVec4 color;
            switch (g_status) {
                case Status::Success:       color = ImVec4(0,1,0,1); break;
                case Status::Failed:        color = ImVec4(1,0,0,1); break;
                case Status::NetworkError:  color = ImVec4(1,0,0,1); break;
                case Status::Expired:       color = ImVec4(1,0.5f,0,1); break;
                case Status::Verifying:     color = ImVec4(1,1,0,1); break;
                default:                    color = ImVec4(1,1,1,1); break;
            }
            ImGui::TextColored(color, "%s", g_message.c_str());

            ImGui::Separator();
            ImGui::Text("如果激活后功能未出现，请尝试手动初始化:");
            if (ImGui::Button("手动初始化", ImVec2(-1, 0))) {
                 AddPostActivationTask([]() {
                    XwzcMods::DoMainInit();
                });
            }

            ImGui::EndPopup();
        }
    }

    // Base64 编解码实现
    static const std::string base64_chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
    
    std::string base64_encode(const std::string& in) {
        std::string out;
        int val = 0, valb = -6;
        for (unsigned char c : in) {
            val = (val << 8) + c;
            valb += 8;
            while (valb >= 0) {
                out.push_back(base64_chars[(val >> valb) & 0x3F]);
                valb -= 6;
            }
        }
        if (valb > -6) out.push_back(base64_chars[((val << 8) >> (valb + 8)) & 0x3F]);
        while (out.size() % 4) out.push_back('=');
        return out;
    }

    std::string base64_decode(const std::string& in) {
        std::string out;
        std::vector<int> T(256, -1);
        for (int i = 0; i < 64; i++) T[base64_chars[i]] = i;

        int val = 0, valb = -8;
        for (char c : in) {
            if (T[c] == -1) break;
            val = (val << 6) + T[c];
            valb += 6;
            if (valb >= 0) {
                out.push_back(char((val >> valb) & 0xFF));
                valb -= 8;
            }
        }
        return out;
    }

    // 3. 实现这个辅助函数
    void set_activation_code(const char* code) {
        strncpy(&g_activation_code[0], code, g_activation_code.size() - 1);
        // 确保字符串总是以空字符结尾
        g_activation_code[g_activation_code.size() - 1] = '\0';
    }
} 