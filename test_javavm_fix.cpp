// 测试JavaVM前向声明修复
#include "app/src/main/cpp/include/gameHack/ActivationManager.h"

int main() {
    // 测试初始化
    ActivationManager::Init();
    
    // 测试设备ID生成
    std::string deviceId = ActivationManager::GenerateDeviceId();
    
    // 测试复制功能
    ActivationManager::CopyToClipboard(deviceId);
    
    // 测试激活码更新
    ActivationManager::updateActivationCode("ABCD-EFGH-IJKL-MNOP");
    
    // 测试获取当前激活码
    std::string currentCode = ActivationManager::getCurrentActivationCode();
    
    // 测试激活设备
    bool result = ActivationManager::ActivateDevice("ABCD-EFGH-IJKL-MNOP");
    
    return 0;
}
