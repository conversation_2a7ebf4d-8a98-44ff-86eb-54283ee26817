# Android Xposed ImGui 激活系统 - 完整重构版

## 🎯 重构完成的功能

### 1. 🎨 全新激活界面设计
- **美观的UI布局**：使用现代化的ImGui界面设计
- **图标化显示**：所有状态和按钮都有对应的emoji图标
- **颜色主题**：统一的颜色方案，提升用户体验
- **响应式布局**：自适应窗口大小

### 2. 🔐 真正的AES加密实现
- **前后端一致**：使用相同的AES-128-CBC加密算法
- **固定密钥**：`XwzcActivation24` (16字节)
- **固定IV**：`InitVector123456` (16字节)
- **Java实现**：通过CryptoUtils类提供加密服务
- **C++调用**：通过JNI调用Java加密方法
- **向后兼容**：加密失败时自动降级到Base64编码

### 3. ⌨️ 完善的输入法系统
- **自动初始化**：在目标应用启动时自动初始化InputHelper
- **隐藏EditText**：创建不可见的输入框来触发系统输入法
- **JNI桥接**：C++和Java之间的双向通信
- **错误处理**：完善的异常处理和日志记录

### 4. 📋 剪贴板功能
- **复制设备ID**：一键复制设备ID到剪贴板
- **粘贴激活码**：从剪贴板读取并自动格式化激活码
- **格式验证**：自动验证和格式化激活码格式
- **状态反馈**：操作成功/失败的即时反馈

## 🏗️ 技术架构

### Java层 (Android)
```
InputHelper.java     - 输入法和剪贴板管理
CryptoUtils.java     - AES加密解密实现
Core.kt             - Xposed模块入口和初始化
```

### C++层 (Native)
```
ActivationManager.cpp - 激活管理核心逻辑
jni_bridge.cpp       - JNI桥接实现
```

### JNI方法映射
```
Java -> C++:
- setJavaVM()           设置JavaVM指针
- onInputComplete()     输入完成回调
- getCurrentActivationCode() 获取当前激活码

C++ -> Java:
- jniShowInputMethod()  显示输入法
- jniCopyToClipboard()  复制到剪贴板
- jniGetFromClipboard() 从剪贴板获取
- jniPasteActivationCode() 粘贴激活码
- jniAESEncrypt()       AES加密
- jniAESDecrypt()       AES解密
```

## 🚀 使用说明

### 1. 编译和安装
```bash
# 编译项目
./gradlew assembleRelease

# 安装到设备
adb install app/build/outputs/apk/release/app-release.apk

# 在Xposed管理器中激活模块
# 重启设备
```

### 2. 激活流程
1. **启动目标应用**：希望之村等Unity游戏
2. **查看设备ID**：在激活界面复制设备ID
3. **联系管理员**：将设备ID发送给管理员绑定
4. **输入激活码**：点击输入框调出系统输入法
5. **粘贴功能**：可以从剪贴板直接粘贴激活码
6. **激活验证**：点击激活按钮进行网络验证

### 3. 界面功能
- **🔑 激活码输入**：点击按钮调出输入法
- **📋 复制设备ID**：一键复制到剪贴板
- **📋 粘贴激活码**：从剪贴板自动读取和格式化
- **🗑️ 清空输入**：清空当前输入的激活码
- **🚀 激活设备**：提交激活请求到服务器
- **🔄 网络验证**：实时显示验证状态

## 🔧 调试信息

### 日志标签
```
ActivationManager  - 激活管理器日志
InputHelper       - 输入法辅助日志
CryptoUtils       - 加密工具日志
JNI_Bridge        - JNI桥接日志
XposedImgui       - Xposed模块日志
```

### 常见问题排查
1. **输入法无法调出**：检查InputHelper是否正确初始化
2. **复制功能失效**：检查JNI桥接是否正常工作
3. **加密失败**：查看CryptoUtils测试结果
4. **网络连接失败**：检查服务器地址和端口配置

## 📝 更新日志

### v2.0.0 (当前版本)
- ✅ 重构激活界面UI设计
- ✅ 实现真正的AES加密
- ✅ 修复输入法调用问题
- ✅ 完善剪贴板功能
- ✅ 优化JNI桥接
- ✅ 添加完整的错误处理
- ✅ 改进用户体验

### v1.0.0 (之前版本)
- 基础激活功能
- 简单的Base64编码
- 基本的UI界面

## 🔒 安全特性

- **AES-128-CBC加密**：所有网络通信都经过加密
- **固定密钥管理**：密钥和IV与后端服务器同步
- **设备ID绑定**：基于硬件信息生成唯一设备ID
- **时间验证**：激活码有有效期限制
- **网络验证**：定期验证激活状态

## 📞 技术支持

如有问题请联系开发团队，并提供以下信息：
- 设备型号和Android版本
- Xposed框架版本
- 相关日志信息
- 问题复现步骤
