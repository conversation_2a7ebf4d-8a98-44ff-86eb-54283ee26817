# 🧪 Android Xposed ImGui 激活系统测试指南

## ✅ 编译状态
**状态**: 编译成功 ✅  
**时间**: 2024年12月  
**版本**: v2.0.0

## 🚀 快速测试步骤

### 1. 安装和激活
```bash
# 1. 安装APK到设备
adb install app/build/outputs/apk/debug/app-debug.apk

# 2. 在Xposed管理器中激活模块
# 3. 重启设备
adb reboot
```

### 2. 功能测试清单

#### ✅ 基础功能测试
- [ ] **模块加载**: 启动目标应用，检查Xposed日志是否显示模块加载成功
- [ ] **激活界面**: 确认激活界面正常显示，UI美观
- [ ] **设备ID生成**: 检查设备ID是否正确生成和显示

#### ✅ 输入法功能测试
- [ ] **输入法调用**: 点击激活码输入按钮，检查是否能正常调出系统输入法
- [ ] **输入回调**: 输入激活码后，检查是否能正确回调到C++层
- [ ] **格式化**: 输入的激活码是否自动格式化为XXXX-XXXX-XXXX-XXXX格式

#### ✅ 剪贴板功能测试
- [ ] **复制设备ID**: 点击复制按钮，检查设备ID是否成功复制到剪贴板
- [ ] **粘贴激活码**: 从剪贴板粘贴激活码，检查是否自动格式化和填入
- [ ] **状态反馈**: 复制和粘贴操作是否有正确的状态提示

#### ✅ 加密功能测试
- [ ] **AES加密**: 检查日志确认使用AES加密而非Base64
- [ ] **加密测试**: CryptoUtils.testCrypto()是否返回true
- [ ] **网络通信**: 激活请求是否正确加密发送

#### ✅ 网络功能测试
- [ ] **服务器连接**: 检查是否能连接到激活服务器
- [ ] **激活验证**: 输入正确激活码是否能成功激活
- [ ] **状态同步**: 激活状态是否正确保存和显示

## 🔍 调试信息检查

### 关键日志标签
```bash
# 查看所有相关日志
adb logcat | grep -E "(ActivationManager|InputHelper|CryptoUtils|JNI_Bridge|XposedImgui)"

# 查看激活管理器日志
adb logcat | grep "ActivationManager"

# 查看输入法日志
adb logcat | grep "InputHelper"

# 查看加密日志
adb logcat | grep "CryptoUtils"

# 查看JNI桥接日志
adb logcat | grep "JNI_Bridge"
```

### 预期日志输出
```
I/JNI_Bridge: JNI Bridge initialized with JavaVM: 0x...
I/XposedImgui: InputHelper initialized successfully in MainActivity
I/CryptoUtils: Crypto test result: true
I/ActivationManager: JNI initialized successfully
I/ActivationManager: AES encryption successful
I/InputHelper: Input method shown with text: 
I/ActivationManager: Clipboard copy result: success
```

## 🐛 常见问题排查

### 1. 输入法无法调出
**症状**: 点击激活码输入按钮无反应  
**排查**:
- 检查InputHelper是否正确初始化
- 查看JNI桥接是否正常工作
- 确认hiddenEditText是否创建成功

### 2. 复制功能失效
**症状**: 点击复制按钮无效果  
**排查**:
- 检查剪贴板权限
- 查看JNI方法调用是否成功
- 确认ClipboardManager是否正确获取

### 3. 加密失败
**症状**: 网络请求使用Base64而非AES  
**排查**:
- 检查CryptoUtils.testCrypto()结果
- 查看AES加密方法是否正确调用
- 确认密钥和IV是否正确设置

### 4. 网络连接失败
**症状**: 激活时显示网络连接失败  
**排查**:
- 检查服务器地址和端口配置
- 确认网络权限
- 查看加密数据格式是否正确

## 📊 性能测试

### 内存使用
- 激活界面内存占用应保持在合理范围
- 无内存泄漏

### 响应速度
- 界面操作响应时间 < 100ms
- 网络请求超时时间 = 10s
- 输入法调出时间 < 500ms

## 🎯 测试通过标准

### 必须通过的测试
1. ✅ 编译无错误
2. ✅ 模块正常加载
3. ✅ 激活界面正常显示
4. ✅ 输入法正常调用
5. ✅ 剪贴板功能正常
6. ✅ AES加密正常工作
7. ✅ 网络通信正常

### 可选测试
- UI美观度
- 动画流畅度
- 错误提示友好度
- 日志信息完整度

## 📞 问题反馈

如发现问题，请提供：
1. 设备信息（型号、Android版本、Xposed版本）
2. 完整的logcat日志
3. 问题复现步骤
4. 预期行为和实际行为描述

---

**测试完成后，请在此文档中标记通过的测试项目 ✅**
